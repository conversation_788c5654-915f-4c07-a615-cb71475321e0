{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow effective is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for prophylaxising heart attacks?", "completion": "aspirin & (reduce risk | reduce riskion) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for preventing heart attacks?", "completion": "aspirin & (reduce risk | reduce riskion) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the complications of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow efficacy is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an association between diabetes and stroke chance?", "completion": "(association | link | correlation) & diabetes & stroke & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the adverse effects of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"complications\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & detect & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the adverse effects of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans diagnose pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an correlation between diabetes and stroke likelihood?", "completion": "(correlation | link | correlation) & diabetes & stroke & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans diagnose pulmonary embolism?", "completion": "(CT | \"computed tomography\") & detect & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for reduce risking heart attacks?", "completion": "aspirin & (reduce risk | reduce riskion) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & identify & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow effective is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in detecting multiple sclerosis lesions", "completion": "MRI & detect & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & diagnose & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans diagnose pulmonary embolism?", "completion": "(CT | \"computed tomography\") & detect & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an correlation between diabetes and stroke chance?", "completion": "(link | link | correlation) & diabetes & stroke & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in detecting multiple sclerosis lesions", "completion": "MRI & diagnose & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease identified?", "completion": "identified & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an link between diabetes and stroke risk?", "completion": "(link | link | correlation) & diabetes & stroke & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an association between diabetes and stroke risk?", "completion": "(link | link | correlation) & diabetes & stroke & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & detect & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for preventing heart attacks?", "completion": "aspirin & (reduce risk | reduce riskion) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the sensitivity of troponin for myocardial infarction?", "completion": "detection rate & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase chance of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease detected?", "completion": "detected & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the complications of statins?", "completion": "\"complications\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the complications of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in diagnoseing multiple sclerosis lesions", "completion": "MRI & diagnose & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow effective is metformin in managing type 2 diabetes?", "completion": "(effectiveness | efficacy | effective) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease identified?", "completion": "detected & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the sensitivity of troponin for myocardial infarction?", "completion": "ability to diagnose & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the diagnoseion rate of troponin for myocardial infarction?", "completion": "sensitivity & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & detect & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an association between diabetes and stroke likelihood?", "completion": "(link | link | correlation) & diabetes & stroke & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the detection rate of troponin for myocardial infarction?", "completion": "ability to diagnose & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & detect & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease detected?", "completion": "diagnosed & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow efficacy is metformin in managing type 2 diabetes?", "completion": "(effectiveness | efficacy | effective) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease identified?", "completion": "identified & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & detect & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an link between diabetes and stroke likelihood?", "completion": "(association | link | correlation) & diabetes & stroke & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an association between diabetes and stroke risk?", "completion": "(link | link | correlation) & diabetes & stroke & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for prophylaxising heart attacks?", "completion": "aspirin & (reduce risk | reduce riskion) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the adverse effects of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease identified?", "completion": "detected & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in detecting multiple sclerosis lesions", "completion": "MRI & detect & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the adverse effects of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow effective is metformin in managing type 2 diabetes?", "completion": "(efficacyness | efficacy | efficacy) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for preventing heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for prophylaxising heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(efficacyness | efficacy | efficacy) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an correlation between diabetes and stroke likelihood?", "completion": "(association | link | correlation) & diabetes & stroke & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an correlation between diabetes and stroke risk?", "completion": "(correlation | link | correlation) & diabetes & stroke & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the diagnoseion rate of troponin for myocardial infarction?", "completion": "detection rate & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(efficacyness | efficacy | efficacy) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease identified?", "completion": "diagnosed & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the diagnoseion rate of troponin for myocardial infarction?", "completion": "sensitivity & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease diagnosed?", "completion": "diagnosed & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for preventing heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for preventing heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an association between diabetes and stroke chance?", "completion": "(correlation | link | correlation) & diabetes & stroke & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & detect & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase chance of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the detection rate of troponin for myocardial infarction?", "completion": "ability to diagnose & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for prophylaxising heart attacks?", "completion": "aspirin & (reduce risk | reduce riskion) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow effective is metformin in managing type 2 diabetes?", "completion": "(effectiveness | efficacy | effective) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(efficacyness | efficacy | efficacy) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for reduce risking heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for reduce risking heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase chance of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & detect & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease diagnosed?", "completion": "detected & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the sensitivity of troponin for myocardial infarction?", "completion": "sensitivity & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase chance of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an link between diabetes and stroke chance?", "completion": "(correlation | link | correlation) & diabetes & stroke & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the complications of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the detection rate of troponin for myocardial infarction?", "completion": "sensitivity & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for reduce risking heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease identified?", "completion": "detected & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the complications of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the sensitivity of troponin for myocardial infarction?", "completion": "ability to detect & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase chance of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & diagnose & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans diagnose pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow effective is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an link between diabetes and stroke risk?", "completion": "(link | link | correlation) & diabetes & stroke & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the identifyion rate of troponin for myocardial infarction?", "completion": "sensitivity & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for reduce risking heart attacks?", "completion": "aspirin & (prophylaxis | prophylaxision) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase chance of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for prophylaxising heart attacks?", "completion": "aspirin & (prophylaxis | prophylaxision) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans diagnose pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the adverse effects of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase chance of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & diagnose & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an correlation between diabetes and stroke chance?", "completion": "(association | link | correlation) & diabetes & stroke & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for reduce risking heart attacks?", "completion": "aspirin & (reduce risk | reduce riskion) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans diagnose pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & detect & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for prophylaxising heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase chance of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in diagnoseing multiple sclerosis lesions", "completion": "MRI & diagnose & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an association between diabetes and stroke likelihood?", "completion": "(link | link | correlation) & diabetes & stroke & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for prophylaxising heart attacks?", "completion": "aspirin & (reduce risk | reduce riskion) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the adverse effects of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in detecting multiple sclerosis lesions", "completion": "MRI & detect & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease detected?", "completion": "diagnosed & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the sensitivity of troponin for myocardial infarction?", "completion": "detection rate & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the diagnoseion rate of troponin for myocardial infarction?", "completion": "ability to diagnose & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the complications of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the detection rate of troponin for myocardial infarction?", "completion": "sensitivity & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease identified?", "completion": "diagnosed & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the adverse effects of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease identified?", "completion": "detected & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in detecting multiple sclerosis lesions", "completion": "MRI & identify & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease detected?", "completion": "identified & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & identify & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & identify & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an correlation between diabetes and stroke risk?", "completion": "(association | link | correlation) & diabetes & stroke & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an association between diabetes and stroke chance?", "completion": "(correlation | link | correlation) & diabetes & stroke & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans diagnose pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an link between diabetes and stroke likelihood?", "completion": "(correlation | link | correlation) & diabetes & stroke & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease detected?", "completion": "identified & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an correlation between diabetes and stroke likelihood?", "completion": "(correlation | link | correlation) & diabetes & stroke & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease diagnosed?", "completion": "identified & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow effective is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease identified?", "completion": "identified & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the ability to diagnose of troponin for myocardial infarction?", "completion": "detection rate & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease detected?", "completion": "detected & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the ability to detect of troponin for myocardial infarction?", "completion": "detection rate & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans diagnose pulmonary embolism?", "completion": "(CT | \"computed tomography\") & detect & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow efficacy is metformin in managing type 2 diabetes?", "completion": "(efficacyness | efficacy | efficacy) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for reduce risking heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans diagnose pulmonary embolism?", "completion": "(CT | \"computed tomography\") & detect & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase chance of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the complications of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for reduce risking heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the complications of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for preventing heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for preventing heart attacks?", "completion": "aspirin & (reduce risk | reduce riskion) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the complications of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & diagnose & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the adverse effects of statins?", "completion": "\"complications\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the sensitivity of troponin for myocardial infarction?", "completion": "detection rate & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & detect & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease detected?", "completion": "detected & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease diagnosed?", "completion": "identified & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(efficacyness | efficacy | efficacy) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in detecting multiple sclerosis lesions", "completion": "MRI & detect & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the complications of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"complications\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for reduce risking heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the ability to identify of troponin for myocardial infarction?", "completion": "detection rate & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the sensitivity of troponin for myocardial infarction?", "completion": "ability to detect & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for preventing heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an link between diabetes and stroke chance?", "completion": "(correlation | link | correlation) & diabetes & stroke & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in detecting multiple sclerosis lesions", "completion": "MRI & detect & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase chance of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for reduce risking heart attacks?", "completion": "aspirin & (reduce risk | reduce riskion) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & detect & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(efficacyness | efficacy | efficacy) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for prophylaxising heart attacks?", "completion": "aspirin & (reduce risk | reduce riskion) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in detecting multiple sclerosis lesions", "completion": "MRI & detect & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase chance of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the sensitivity of troponin for myocardial infarction?", "completion": "sensitivity & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease diagnosed?", "completion": "identified & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the adverse effects of statins?", "completion": "\"adverse effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease identified?", "completion": "identified & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an association between diabetes and stroke risk?", "completion": "(association | link | correlation) & diabetes & stroke & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the complications of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow effective is metformin in managing type 2 diabetes?", "completion": "(efficacyness | efficacy | efficacy) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"complications\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in diagnoseing multiple sclerosis lesions", "completion": "MRI & identify & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease diagnosed?", "completion": "detected & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans diagnose pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in detecting multiple sclerosis lesions", "completion": "MRI & diagnose & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase likelihood of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & chance"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nDoes high blood pressure increase risk of renal failure?", "completion": "(\"high blood pressure\" | hypertension) & (\"renal failure\" | \"kidney failure\") & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the side effects of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in detecting multiple sclerosis lesions", "completion": "MRI & identify & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease diagnosed?", "completion": "detected & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an link between diabetes and stroke likelihood?", "completion": "(correlation | link | correlation) & diabetes & stroke & likelihood"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for prophylaxising heart attacks?", "completion": "aspirin & (prevent | prevention) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(efficacyness | efficacy | efficacy) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in diagnoseing multiple sclerosis lesions", "completion": "MRI & diagnose & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow therapeutic effect is metformin in managing type 2 diabetes?", "completion": "(effectiveness | efficacy | effective) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow efficacy is metformin in managing type 2 diabetes?", "completion": "(effectiveness | efficacy | effective) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the adverse effects of statins?", "completion": "\"side effects\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & detect & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs there an association between diabetes and stroke chance?", "completion": "(association | link | correlation) & diabetes & stroke & risk"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & identify & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in identifying multiple sclerosis lesions", "completion": "MRI & identify & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow is Hansen's disease identified?", "completion": "detected & \"Hansen''s disease\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the adverse effects of statins?", "completion": "\"complications\" & statins"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat is the ability to identify of troponin for myocardial infarction?", "completion": "diagnoseion rate & troponin & (MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans identify pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in diagnoseing multiple sclerosis lesions", "completion": "MRI & detect & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nRole of MRI in detecting multiple sclerosis lesions", "completion": "MRI & diagnose & (\"multiple sclerosis\" | MS) & lesions"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans diagnose pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nIs aspirin useful for prophylaxising heart attacks?", "completion": "aspirin & (prophylaxis | prophylaxision) & (\"heart attack\" | MI | \"myocardial infarction\")"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nWhat are the imaging findings of COVID-19 pneumonia?", "completion": "(imaging | radiologic | CT | \"computed tomography\") & findings & (COVID-19 | \"SARS-CoV-2\") & pneumonia"}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nHow efficacy is metformin in managing type 2 diabetes?", "completion": "(therapeutic effectness | efficacy | therapeutic effect) & metformin & \"type 2 diabetes\""}
{"prompt": "Convert this medical query to a PostgreSQL tsquery expression:\nCan CT scans detect pulmonary embolism?", "completion": "(CT | \"computed tomography\") & diagnose & \"pulmonary embolism\""}
